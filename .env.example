# GENERAL CONFIGURATION
PORT=9000
NODE_ENV=development
SECRET=
LOG_LEVEL=DEBUG

# DATABASE CONFIGURATION
MYSQL_DB_HOST=
MYSQL_DBNAME=
MYSQL_USERNAME=
MYSQL_PASSWORD=
MYSQL_DB_PORT=

# REDIS & MESSAGE BROKER CONFIGURATION
REDIS_URL=

ENCRYPT_SECRET=

# TELEGRAM NOTIFICATION CONFIGURATION
# Error Bot - dedicated for error notifications
TELEGRAM_ERROR_BOT_TOKEN=
TELEGRAM_ERROR_CHAT_ID=
TELEGRAM_ERROR_PARSE_MODE=HTML
TELEGRAM_ERROR_DISABLE_WEB_PREVIEW=true

# General Bot - for all other notifications (success, warnings, general messages)
TELEGRAM_GENERAL_BOT_TOKEN=
TELEGRAM_GENERAL_CHAT_ID=
TELEGRAM_GENERAL_PARSE_MODE=HTML
TELEGRAM_GENERAL_DISABLE_WEB_PREVIEW=true

# Legacy single bot configuration (fallback if new ones aren't set)
TELEGRAM_BOT_TOKEN=
TELEGRAM_DEFAULT_CHAT_ID=
TELEGRAM_PARSE_MODE=HTML
TELEGRAM_DISABLE_WEB_PREVIEW=true

# SOCKET CONFIGURATION
SOCKET_URL=
SOCKET_RECONNECTION=true
SOCKET_RECONNECTION_ATTEMPTS=5
SOCKET_RECONNECTION_DELAY=1000
SOCKET_TIMEOUT=20000
SOCKET_TRANSPORTS=websocket,polling
# SOCKET_AUTH={"token":"your-auth-token"}

# MONGODB CONFIGURATION
MONGODB_URI=mongodb://localhost:27017
MONGODB_DATABASE=your_database_name
MONGODB_MAX_POOL_SIZE=10
MONGODB_SERVER_SELECTION_TIMEOUT=5000
MONGODB_SOCKET_TIMEOUT=45000
MONGODB_CONNECT_TIMEOUT=10000
MONGODB_MAX_IDLE_TIME=30000
MONGODB_RETRY_WRITES=true
MONGODB_RETRY_READS=true

# LEGACY TELEGRAM TOKENS (for backward compatibility)
TELEGRAM_TRADER_TOKEN=
TELEGRAM_CUSTOMER_TOKEN=

SERVER_IP=