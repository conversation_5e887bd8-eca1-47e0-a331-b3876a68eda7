import { ServiceManagerConfig } from '@services/ServiceManager';
import { TelegramParseMode, SocketTransport } from '../types/enums';

/**
 * Service configuration factory
 */
export function createServiceConfig(): ServiceManagerConfig {
  const config: ServiceManagerConfig = {};

  // Telegram configuration - support for two separate bots
  const telegramConfig: { error?: any; general?: any } = {};

  // Error bot configuration
  if (process.env.TELEGRAM_ERROR_BOT_TOKEN) {
    telegramConfig.error = {
      token: process.env.TELEGRAM_ERROR_BOT_TOKEN,
      defaultChatId: process.env.TELEGRAM_ERROR_CHAT_ID,
      parseMode: (process.env.TELEGRAM_ERROR_PARSE_MODE as TelegramParseMode) || TelegramParseMode.HTML,
      disableWebPagePreview: process.env.TELEGRAM_ERROR_DISABLE_WEB_PREVIEW === 'true',
    };
  }

  // General bot configuration
  if (process.env.TELEGRAM_GENERAL_BOT_TOKEN) {
    telegramConfig.general = {
      token: process.env.TELEGRAM_GENERAL_BOT_TOKEN,
      defaultChatId: process.env.TELEGRAM_GENERAL_CHAT_ID,
      parseMode: (process.env.TELEGRAM_GENERAL_PARSE_MODE as TelegramParseMode) || TelegramParseMode.HTML,
      disableWebPagePreview: process.env.TELEGRAM_GENERAL_DISABLE_WEB_PREVIEW === 'true',
    };
  }

  // Fallback to legacy single bot configuration if new ones aren't set
  if (!telegramConfig.error && !telegramConfig.general && process.env.TELEGRAM_BOT_TOKEN) {
    telegramConfig.general = {
      token: process.env.TELEGRAM_BOT_TOKEN,
      defaultChatId: process.env.TELEGRAM_DEFAULT_CHAT_ID,
      parseMode: (process.env.TELEGRAM_PARSE_MODE as TelegramParseMode) || TelegramParseMode.HTML,
      disableWebPagePreview: process.env.TELEGRAM_DISABLE_WEB_PREVIEW === 'true',
    };
  }

  if (telegramConfig.error || telegramConfig.general) {
    config.telegram = telegramConfig;
  }

  // Socket configuration
  if (process.env.SOCKET_URL) {
    config.socket = {
      url: process.env.SOCKET_URL,
      options: {
        reconnection: process.env.SOCKET_RECONNECTION !== 'false',
        reconnectionAttempts: parseInt(process.env.SOCKET_RECONNECTION_ATTEMPTS || '5'),
        reconnectionDelay: parseInt(process.env.SOCKET_RECONNECTION_DELAY || '1000'),
        timeout: parseInt(process.env.SOCKET_TIMEOUT || '20000'),
        transports: process.env.SOCKET_TRANSPORTS?.split(',').map((t) => t.trim() as SocketTransport) || [
          SocketTransport.WEBSOCKET,
          SocketTransport.POLLING,
        ],
        auth: process.env.SOCKET_AUTH ? JSON.parse(process.env.SOCKET_AUTH) : undefined,
      },
    };
  }

  // MongoDB configuration
  if (process.env.MONGODB_URI && process.env.MONGODB_DATABASE) {
    config.mongodb = {
      uri: process.env.MONGODB_URI,
      databaseName: process.env.MONGODB_DATABASE,
      options: {
        maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '10'),
        serverSelectionTimeoutMS: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT || '5000'),
        socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT || '45000'),
        connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT || '10000'),
        maxIdleTimeMS: parseInt(process.env.MONGODB_MAX_IDLE_TIME || '30000'),
        retryWrites: process.env.MONGODB_RETRY_WRITES !== 'false',
        retryReads: process.env.MONGODB_RETRY_READS !== 'false',
      },
    };
  }

  return config;
}

/**
 * Validate service configuration
 */
export function validateServiceConfig(config: ServiceManagerConfig): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate Telegram config
  if (config.telegram) {
    // Validate error bot config
    if (config.telegram.error) {
      if (!config.telegram.error.token) {
        errors.push('Telegram error bot token is required');
      }
      if (
        config.telegram.error.parseMode &&
        !Object.values(TelegramParseMode).includes(config.telegram.error.parseMode)
      ) {
        errors.push('Invalid Telegram error bot parse mode');
      }
    }

    // Validate general bot config
    if (config.telegram.general) {
      if (!config.telegram.general.token) {
        errors.push('Telegram general bot token is required');
      }
      if (
        config.telegram.general.parseMode &&
        !Object.values(TelegramParseMode).includes(config.telegram.general.parseMode)
      ) {
        errors.push('Invalid Telegram general bot parse mode');
      }
    }

    // Ensure at least one bot is configured
    if (!config.telegram.error && !config.telegram.general) {
      errors.push('At least one Telegram bot (error or general) must be configured');
    }
  }

  // Validate Socket config
  if (config.socket) {
    if (!config.socket.url) {
      errors.push('Socket URL is required');
    }
    try {
      new URL(config.socket.url);
    } catch {
      errors.push('Invalid Socket URL format');
    }
  }

  // Validate MongoDB config
  if (config.mongodb) {
    if (!config.mongodb.uri) {
      errors.push('MongoDB URI is required');
    }
    if (!config.mongodb.databaseName) {
      errors.push('MongoDB database name is required');
    }
    if (config.mongodb.uri && !config.mongodb.uri.startsWith('mongodb')) {
      errors.push('Invalid MongoDB URI format - must start with mongodb:// or mongodb+srv://');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
