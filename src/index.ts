/**
 * Main entry point for the application
 */

import dotenv from 'dotenv';
import Logger from './libs/Logger';
import { ServiceManager } from '@services/ServiceManager';
import { createServiceConfig, validateServiceConfig } from '@config/services';
import { handleErrorException } from '@libs/Utils';
import { NotificationType } from './types/enums';

// Load environment variables
dotenv.config();

let serviceManager: ServiceManager | null = null;

/**
 * Application entry point
 */
async function main(): Promise<void> {
  try {
    Logger.info('Application starting...');

    // Create and validate service configuration
    const serviceConfig = createServiceConfig();
    const validation = validateServiceConfig(serviceConfig);

    if (!validation.valid) {
      Logger.error('Invalid service configuration:', validation.errors);
      throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
    }

    // Initialize services
    serviceManager = new ServiceManager(serviceConfig);
    await serviceManager.initialize();

    // Setup graceful shutdown
    setupGracefulShutdown();

    // Example usage of services
    await demonstrateServices();

    Logger.info('Application started successfully');
  } catch (error) {
    Logger.error('Failed to start application:', error);

    // Send error notification if possible
    if (serviceManager?.telegramError || serviceManager?.telegramGeneral) {
      await serviceManager
        .sendNotification(
          NotificationType.ERROR,
          'Application Startup Failed',
          error instanceof Error ? error.message : 'Unknown error',
        )
        .catch(() => {
          // Ignore notification errors during startup failure
        });
    }

    process.exit(1);
  }
}

/**
 * Demonstrate service usage
 */
async function demonstrateServices(): Promise<void> {
  if (!serviceManager) return;

  try {
    // Example: Send a test notification
    if (serviceManager.telegramGeneral) {
      Logger.info('Sending test notification...');
      await serviceManager.sendNotification(
        NotificationType.SUCCESS,
        'Application Started',
        'All services are running and ready to handle requests',
      );
    }

    // Example: Send a socket message
    if (serviceManager.socket) {
      Logger.info('Sending test socket message...');
      await serviceManager.sendSocketMessage('app_status', {
        status: 'running',
        timestamp: new Date().toISOString(),
        services: serviceManager.getStatus(),
      });
    }

    // Example: Health check
    const health = await serviceManager.healthCheck();
    Logger.info('Service health check:', health);
  } catch (error) {
    Logger.error('Error in service demonstration:', error);
    handleErrorException('Service Demonstration Error', error);
  }
}

/**
 * Setup graceful shutdown handlers
 */
function setupGracefulShutdown(): void {
  const shutdown = async (signal: string) => {
    Logger.info(`Received ${signal}, shutting down gracefully...`);

    try {
      if (serviceManager) {
        await serviceManager.shutdown();
      }
      Logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      Logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    Logger.error('Uncaught exception:', error);
    handleErrorException('Uncaught Exception', error);
    process.exit(1);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    Logger.error('Unhandled rejection at:', promise, 'reason:', reason);
    handleErrorException('Unhandled Promise Rejection', reason);
    process.exit(1);
  });
}

// Start the application if this file is run directly
if (require.main === module) {
  main().catch((error) => {
    Logger.error('Unhandled error:', error);
    process.exit(1);
  });
}

export default main;
export { serviceManager };
