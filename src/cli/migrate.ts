#!/usr/bin/env node

/**
 * Migration CLI Tool
 *
 * Usage:
 *   npm run migrate up              # Run all pending migrations
 *   npm run migrate down <id>       # Rollback specific migration
 *   npm run migrate status          # Show migration status
 *   npm run migrate validate        # Validate migrations
 *   npm run migrate reset --confirm # Reset all migrations (dangerous)
 */

import dotenv from 'dotenv';
import { MongoDBService } from '../services/MongoDBService';
import { MigrationRunner } from '../services/MigrationRunner';
import { migrations, validateMigrations } from '../migrations';
import Logger from '../libs/Logger';

// Load environment variables
dotenv.config();

interface CliArgs {
  command: string;
  migrationId?: string;
  dryRun?: boolean;
  force?: boolean;
  confirm?: boolean;
  targetVersion?: string;
}

class MigrationCLI {
  private mongoService: MongoDBService;
  private migrationRunner: MigrationRunner;

  constructor() {
    if (!process.env.MONGODB_URI || !process.env.MONGODB_DATABASE) {
      console.error('Error: MONGODB_URI and MONGODB_DATABASE environment variables are required');
      process.exit(1);
    }

    this.mongoService = new MongoDBService({
      uri: process.env.MONGODB_URI,
      databaseName: process.env.MONGODB_DATABASE,
    });

    this.migrationRunner = new MigrationRunner({
      mongoService: this.mongoService,
    });
  }

  async initialize(): Promise<void> {
    try {
      await this.mongoService.connect();
      await this.migrationRunner.initialize();
      this.migrationRunner.registerMigrations(migrations);
    } catch (error) {
      Logger.error('Failed to initialize migration CLI:', error);
      throw error;
    }
  }

  async runCommand(args: CliArgs): Promise<void> {
    try {
      await this.initialize();

      switch (args.command) {
        case 'up':
          await this.runMigrations(args);
          break;
        case 'down':
          await this.rollbackMigration(args);
          break;
        case 'status':
          await this.showStatus();
          break;
        case 'validate':
          await this.validateMigrations();
          break;
        case 'reset':
          await this.resetMigrations(args);
          break;
        default:
          this.showHelp();
          process.exit(1);
      }
    } catch (error) {
      Logger.error('Migration command failed:', error);
      process.exit(1);
    } finally {
      await this.mongoService.disconnect();
    }
  }

  private async runMigrations(args: CliArgs): Promise<void> {
    console.log('🚀 Running migrations...\n');

    const results = await this.migrationRunner.run({
      dryRun: args.dryRun,
      targetVersion: args.targetVersion,
      force: args.force,
    });

    if (args.dryRun) {
      console.log('✅ Dry run completed\n');
      return;
    }

    const successful = results.filter((r) => r.status === 'completed');
    const failed = results.filter((r) => r.status === 'failed');

    console.log(`\n📊 Migration Results:`);
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);

    if (failed.length > 0) {
      console.log('\n❌ Failed migrations:');
      failed.forEach((result) => {
        console.log(`   - ${result.migrationId}: ${result.error}`);
      });
      process.exit(1);
    }

    console.log('\n✅ All migrations completed successfully!');
  }

  private async rollbackMigration(args: CliArgs): Promise<void> {
    if (!args.migrationId) {
      console.error('Error: Migration ID is required for rollback');
      process.exit(1);
    }

    console.log(`🔄 Rolling back migration: ${args.migrationId}\n`);

    const result = await this.migrationRunner.rollback(args.migrationId);

    if (result.status === 'rolled_back') {
      console.log(`✅ Migration ${args.migrationId} rolled back successfully`);
    } else {
      console.log(`❌ Rollback failed: ${result.error}`);
      process.exit(1);
    }
  }

  private async showStatus(): Promise<void> {
    console.log('📋 Migration Status\n');

    const status = await this.migrationRunner.getStatus();

    console.log(`Total migrations: ${status.total}`);
    console.log(`Executed: ${status.executed}`);
    console.log(`Pending: ${status.pending}\n`);

    if (status.migrations.length === 0) {
      console.log('No migrations found');
      return;
    }

    console.log('Migrations:');
    status.migrations.forEach((migration: any) => {
      const statusIcon = migration.status === 'completed' ? '✅' : '⏳';
      const executedInfo = migration.executedAt ? ` (executed: ${migration.executedAt.toISOString()})` : '';

      console.log(`  ${statusIcon} ${migration.id}: ${migration.name}${executedInfo}`);
    });
  }

  private async validateMigrations(): Promise<void> {
    console.log('🔍 Validating migrations...\n');

    // Validate migration definitions
    const definitionValidation = validateMigrations();

    if (!definitionValidation.valid) {
      console.log('❌ Migration definition validation failed:');
      definitionValidation.errors.forEach((error) => {
        console.log(`   - ${error}`);
      });
      process.exit(1);
    }

    // Validate migration runner
    const runnerValidation = await this.migrationRunner.validate();

    if (!runnerValidation.valid) {
      console.log('❌ Migration runner validation failed:');
      runnerValidation.errors.forEach((error) => {
        console.log(`   - ${error}`);
      });
      process.exit(1);
    }

    console.log('✅ All migrations are valid!');
  }

  private async resetMigrations(args: CliArgs): Promise<void> {
    if (!args.confirm) {
      console.error('Error: Reset requires --confirm flag');
      console.error('This will remove all migration records from the database!');
      process.exit(1);
    }

    console.log('⚠️  Resetting all migration records...\n');

    await this.migrationRunner.reset(true);

    console.log('✅ Migration records reset successfully');
    console.log('⚠️  You may need to re-run migrations');
  }

  private showHelp(): void {
    console.log(`
Migration CLI Tool

Usage:
  npm run migrate <command> [options]

Commands:
  up                    Run all pending migrations
  down <migration-id>   Rollback specific migration
  status                Show migration status
  validate              Validate all migrations
  reset --confirm       Reset all migration records (dangerous)

Options:
  --dry-run            Show what would be executed without running
  --force              Continue even if some migrations fail
  --target <version>   Run migrations up to specific version
  --confirm            Confirm dangerous operations

Examples:
  npm run migrate up
  npm run migrate up --dry-run
  npm run migrate down 001_create_users_collection
  npm run migrate status
  npm run migrate validate
  npm run migrate reset --confirm
`);
  }
}

// Parse command line arguments
function parseArgs(): CliArgs {
  const args = process.argv.slice(2);
  const result: CliArgs = {
    command: args[0] || 'help',
  };

  for (let i = 1; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--dry-run':
        result.dryRun = true;
        break;
      case '--force':
        result.force = true;
        break;
      case '--confirm':
        result.confirm = true;
        break;
      case '--target':
        result.targetVersion = args[++i];
        break;
      default:
        if (!result.migrationId && arg && !arg.startsWith('--')) {
          result.migrationId = arg;
        }
        break;
    }
  }

  return result;
}

// Main execution
async function main(): Promise<void> {
  const args = parseArgs();
  const cli = new MigrationCLI();
  await cli.runCommand(args);
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Migration CLI error:', error);
    process.exit(1);
  });
}

export { MigrationCLI };
