/**
 * Migration Registry
 *
 * This file exports all available migrations in the correct order.
 * Migrations should be imported and added to the migrations array
 * in chronological order.
 */

import { MigrationDefinition } from '../services/MigrationService';
import { createUsersCollectionMigration } from './001_create_users_collection';

/**
 * All available migrations in execution order
 */
export const migrations: MigrationDefinition[] = [
  createUsersCollectionMigration,
  // Add new migrations here in chronological order
];

/**
 * Get migration by ID
 */
export function getMigrationById(id: string): MigrationDefinition | undefined {
  return migrations.find((migration) => migration.id === id);
}

/**
 * Get migrations by version range
 */
export function getMigrationsByVersionRange(fromVersion: string, toVersion: string): MigrationDefinition[] {
  return migrations.filter((migration) => {
    return migration.version >= fromVersion && migration.version <= toVersion;
  });
}

/**
 * Get latest migration
 */
export function getLatestMigration(): MigrationDefinition | undefined {
  if (migrations.length === 0) return undefined;

  return migrations.reduce((latest, current) => {
    return current.version > latest.version ? current : latest;
  });
}

/**
 * Validate migration order and dependencies
 */
export function validateMigrations(): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const migrationIds = new Set<string>();

  // Check for duplicate IDs
  for (const migration of migrations) {
    if (migrationIds.has(migration.id)) {
      errors.push(`Duplicate migration ID: ${migration.id}`);
    }
    migrationIds.add(migration.id);
  }

  // Check version ordering
  for (let i = 1; i < migrations.length; i++) {
    const prev = migrations[i - 1];
    const current = migrations[i];

    if (prev && current && current.version <= prev.version) {
      errors.push(
        `Migration ${current.id} version ${current.version} is not greater than previous migration ${prev.id} version ${prev.version}`,
      );
    }
  }

  // Check dependencies exist
  for (const migration of migrations) {
    if (migration.dependencies) {
      for (const depId of migration.dependencies) {
        if (!migrationIds.has(depId)) {
          errors.push(`Migration ${migration.id} depends on non-existent migration: ${depId}`);
        }
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

export default migrations;
