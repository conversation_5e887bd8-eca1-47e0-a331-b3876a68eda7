import { Db } from 'mongodb';
import { MigrationDefinition } from '../services/MigrationService';

/**
 * Migration: Create Users Collection
 *
 * This migration creates the users collection with:
 * - Schema validation rules
 * - Required indexes for performance and uniqueness
 * - Initial seed data (optional)
 */
export const createUsersCollectionMigration: MigrationDefinition = {
  id: '001_create_users_collection',
  name: 'Create Users Collection',
  description: 'Creates the users collection with schema validation and indexes',
  version: '1.0.0',

  async up(db: Db): Promise<void> {
    const collectionName = 'users';

    // Define schema validation rules
    const validationSchema = {
      $jsonSchema: {
        bsonType: 'object',
        required: ['email', 'username', 'createdAt', 'isActive'],
        properties: {
          email: {
            bsonType: 'string',
            pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
            description: 'Must be a valid email address',
          },
          username: {
            bsonType: 'string',
            minLength: 3,
            maxLength: 30,
            pattern: '^[a-zA-Z0-9_-]+$',
            description: 'Must be 3-30 characters, alphanumeric with underscore and dash',
          },
          firstName: {
            bsonType: 'string',
            minLength: 1,
            maxLength: 50,
            description: 'First name, 1-50 characters',
          },
          lastName: {
            bsonType: 'string',
            minLength: 1,
            maxLength: 50,
            description: 'Last name, 1-50 characters',
          },
          password: {
            bsonType: 'string',
            minLength: 8,
            description: 'Hashed password, minimum 8 characters',
          },
          role: {
            bsonType: 'string',
            enum: ['admin', 'user', 'moderator'],
            description: 'User role',
          },
          isActive: {
            bsonType: 'bool',
            description: 'Whether the user account is active',
          },
          isEmailVerified: {
            bsonType: 'bool',
            description: 'Whether the email has been verified',
          },
          lastLoginAt: {
            bsonType: 'date',
            description: 'Last login timestamp',
          },
          createdAt: {
            bsonType: 'date',
            description: 'Account creation timestamp',
          },
          updatedAt: {
            bsonType: 'date',
            description: 'Last update timestamp',
          },
          profile: {
            bsonType: 'object',
            properties: {
              avatar: {
                bsonType: 'string',
                description: 'Avatar URL',
              },
              bio: {
                bsonType: 'string',
                maxLength: 500,
                description: 'User biography, max 500 characters',
              },
              website: {
                bsonType: 'string',
                description: 'Personal website URL',
              },
              location: {
                bsonType: 'string',
                maxLength: 100,
                description: 'User location, max 100 characters',
              },
            },
            additionalProperties: false,
          },
          preferences: {
            bsonType: 'object',
            properties: {
              theme: {
                bsonType: 'string',
                enum: ['light', 'dark', 'auto'],
                description: 'UI theme preference',
              },
              language: {
                bsonType: 'string',
                pattern: '^[a-z]{2}(-[A-Z]{2})?$',
                description: 'Language preference (ISO 639-1)',
              },
              notifications: {
                bsonType: 'object',
                properties: {
                  email: { bsonType: 'bool' },
                  push: { bsonType: 'bool' },
                  sms: { bsonType: 'bool' },
                },
                additionalProperties: false,
              },
            },
            additionalProperties: false,
          },
          metadata: {
            bsonType: 'object',
            description: 'Additional metadata for the user',
          },
        },
        additionalProperties: false,
      },
    };

    // Create collection with validation
    await db.createCollection(collectionName, {
      validator: validationSchema,
      validationLevel: 'strict',
      validationAction: 'error',
    });

    console.log(`Created collection: ${collectionName} with schema validation`);

    // Get the collection reference
    const usersCollection = db.collection(collectionName);

    // Create indexes for performance and uniqueness

    // Unique indexes
    await usersCollection.createIndex({ email: 1 }, { unique: true, name: 'email_unique' });
    console.log('Created index: email_unique');

    await usersCollection.createIndex({ username: 1 }, { unique: true, name: 'username_unique' });
    console.log('Created index: username_unique');

    // Performance indexes
    await usersCollection.createIndex({ isActive: 1 }, { name: 'isActive_index' });
    console.log('Created index: isActive_index');

    await usersCollection.createIndex({ role: 1 }, { name: 'role_index' });
    console.log('Created index: role_index');

    await usersCollection.createIndex({ createdAt: 1 }, { name: 'createdAt_index' });
    console.log('Created index: createdAt_index');

    await usersCollection.createIndex({ lastLoginAt: 1 }, { name: 'lastLoginAt_index' });
    console.log('Created index: lastLoginAt_index');

    await usersCollection.createIndex({ isEmailVerified: 1 }, { name: 'isEmailVerified_index' });
    console.log('Created index: isEmailVerified_index');

    // Compound indexes
    await usersCollection.createIndex({ isActive: 1, role: 1 }, { name: 'isActive_role_compound' });
    console.log('Created index: isActive_role_compound');

    await usersCollection.createIndex({ email: 1, isActive: 1 }, { name: 'email_isActive_compound' });
    console.log('Created index: email_isActive_compound');

    // Text search index for profile fields
    await usersCollection.createIndex(
      {
        username: 'text',
        firstName: 'text',
        lastName: 'text',
        'profile.bio': 'text',
      },
      {
        name: 'user_text_search',
        weights: {
          username: 10,
          firstName: 5,
          lastName: 5,
          'profile.bio': 1,
        },
      },
    );
    console.log('Created index: user_text_search');

    // Insert initial seed data (admin user)
    const seedUsers = [
      {
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'System',
        lastName: 'Administrator',
        password: '$2b$10$placeholder_hash', // This should be properly hashed
        role: 'admin',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        profile: {
          bio: 'System administrator account',
        },
        preferences: {
          theme: 'dark',
          language: 'en',
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
        },
        metadata: {
          source: 'migration',
          version: '1.0.0',
        },
      },
    ];

    // Insert seed data
    const insertResult = await usersCollection.insertMany(seedUsers);
    console.log(`Inserted ${insertResult.insertedCount} seed users`);

    console.log('Users collection migration completed successfully');
  },

  async down(db: Db): Promise<void> {
    const collectionName = 'users';

    // Drop the entire collection (this will also remove all indexes)
    await db.dropCollection(collectionName);
    console.log(`Dropped collection: ${collectionName}`);

    console.log('Users collection rollback completed successfully');
  },
};
