import { MongoClient, Db, MongoClientOptions, ServerApiVersion, Document } from 'mongodb';
import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';
import { ConnectionState } from '../types/enums';

export interface MongoDBConfig {
  uri: string;
  databaseName: string;
  options?: MongoClientOptions;
}

export class MongoDBService {
  private client: MongoClient | null = null;
  private database: Db | null = null;
  private config: MongoDBConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private connectionPromise: Promise<void> | null = null;

  constructor(config: MongoDBConfig) {
    this.config = {
      ...config,
      options: {
        serverApi: {
          version: ServerApiVersion.v1,
          strict: true,
          deprecationErrors: true,
        },
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4, // Use IPv4, skip trying IPv6
        ...config.options,
      },
    };
  }

  /**
   * Connect to MongoDB
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = this.establishConnection();
    return this.connectionPromise;
  }

  private async establishConnection(): Promise<void> {
    try {
      this.connectionState = ConnectionState.CONNECTING;
      Logger.info(`Connecting to MongoDB: ${this.config.databaseName}`);

      this.client = new MongoClient(this.config.uri, this.config.options);
      await this.client.connect();

      // Verify connection
      await this.client.db('admin').command({ ping: 1 });

      this.database = this.client.db(this.config.databaseName);
      this.connectionState = ConnectionState.CONNECTED;
      this.connectionPromise = null;

      Logger.info(`Successfully connected to MongoDB database: ${this.config.databaseName}`);

      // Setup connection event handlers
      this.setupEventHandlers();
    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.connectionPromise = null;
      Logger.error('Failed to connect to MongoDB:', error);
      throw new BusinessException('MongoDB connection failed', { error, config: this.config.databaseName });
    }
  }

  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('close', () => {
      this.connectionState = ConnectionState.DISCONNECTED;
      Logger.warn('MongoDB connection closed');
    });

    this.client.on('error', (error) => {
      this.connectionState = ConnectionState.ERROR;
      Logger.error('MongoDB connection error:', error);
    });

    this.client.on('timeout', () => {
      Logger.warn('MongoDB connection timeout');
    });

    this.client.on('reconnect', () => {
      this.connectionState = ConnectionState.CONNECTED;
      Logger.info('MongoDB reconnected');
    });
  }

  /**
   * Get the database instance
   */
  async getDatabase(): Promise<Db> {
    await this.ensureConnection();
    if (!this.database) {
      throw new BusinessException('Database not available');
    }
    return this.database;
  }

  /**
   * Get a collection from the database
   */
  async getCollection<T extends Document = Document>(collectionName: string) {
    const db = await this.getDatabase();
    return db.collection<T>(collectionName);
  }

  /**
   * Ensure connection is established
   */
  private async ensureConnection(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED && this.database) {
      return;
    }

    if (this.connectionState === ConnectionState.CONNECTING) {
      if (this.connectionPromise) {
        await this.connectionPromise;
        return;
      }
    }

    await this.connect();
  }

  /**
   * Check if connected to MongoDB
   */
  isConnected(): boolean {
    return this.connectionState === ConnectionState.CONNECTED && this.client !== null;
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * Health check for MongoDB connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected() || !this.client) {
        return false;
      }

      // Ping the database
      await this.client.db('admin').command({ ping: 1 });
      return true;
    } catch (error) {
      Logger.error('MongoDB health check failed:', error);
      return false;
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<any> {
    try {
      const db = await this.getDatabase();
      const stats = await db.stats();
      return {
        database: this.config.databaseName,
        collections: stats.collections,
        dataSize: stats.dataSize,
        storageSize: stats.storageSize,
        indexes: stats.indexes,
        indexSize: stats.indexSize,
        objects: stats.objects,
      };
    } catch (error) {
      Logger.error('Failed to get MongoDB stats:', error);
      throw new BusinessException('Failed to get database statistics', { error });
    }
  }

  /**
   * List all collections in the database
   */
  async listCollections(): Promise<string[]> {
    try {
      const db = await this.getDatabase();
      const collections = await db.listCollections().toArray();
      return collections.map((col) => col.name);
    } catch (error) {
      Logger.error('Failed to list collections:', error);
      throw new BusinessException('Failed to list collections', { error });
    }
  }

  /**
   * Create a collection with optional options
   */
  async createCollection(name: string, options?: any): Promise<void> {
    try {
      const db = await this.getDatabase();
      await db.createCollection(name, options);
      Logger.info(`Created collection: ${name}`);
    } catch (error) {
      Logger.error(`Failed to create collection ${name}:`, error);
      throw new BusinessException('Failed to create collection', { error, collectionName: name });
    }
  }

  /**
   * Drop a collection
   */
  async dropCollection(name: string): Promise<void> {
    try {
      const db = await this.getDatabase();
      await db.dropCollection(name);
      Logger.info(`Dropped collection: ${name}`);
    } catch (error) {
      Logger.error(`Failed to drop collection ${name}:`, error);
      throw new BusinessException('Failed to drop collection', { error, collectionName: name });
    }
  }

  /**
   * Create an index on a collection
   */
  async createIndex(collectionName: string, indexSpec: any, options?: any): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      const result = await collection.createIndex(indexSpec, options);
      Logger.info(`Created index on ${collectionName}:`, result);
    } catch (error) {
      Logger.error(`Failed to create index on ${collectionName}:`, error);
      throw new BusinessException('Failed to create index', { error, collectionName, indexSpec });
    }
  }

  /**
   * Run a database command
   */
  async runCommand(command: any): Promise<any> {
    try {
      const db = await this.getDatabase();
      return await db.command(command);
    } catch (error) {
      Logger.error('Failed to run database command:', error);
      throw new BusinessException('Failed to run database command', { error, command });
    }
  }

  /**
   * Start a session for transactions
   */
  async startSession() {
    await this.ensureConnection();
    if (!this.client) {
      throw new BusinessException('MongoDB client not available');
    }
    return this.client.startSession();
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.close();
        this.connectionState = ConnectionState.DISCONNECTED;
        this.client = null;
        this.database = null;
        Logger.info('Disconnected from MongoDB');
      } catch (error) {
        Logger.error('Error disconnecting from MongoDB:', error);
        throw new BusinessException('Failed to disconnect from MongoDB', { error });
      }
    }
  }

  /**
   * Get connection info
   */
  getConnectionInfo(): {
    state: ConnectionState;
    databaseName: string;
    connected: boolean;
  } {
    return {
      state: this.connectionState,
      databaseName: this.config.databaseName,
      connected: this.isConnected(),
    };
  }
}
