import { MongoDBService } from './MongoDBService';
import { MigrationService, MigrationDefinition, MigrationResult } from './MigrationService';
import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';

export interface MigrationRunnerConfig {
  mongoService: MongoDBService;
  migrationsPath?: string;
  autoRun?: boolean;
  dryRun?: boolean;
}

export class MigrationRunner {
  private migrationService: MigrationService;
  private config: MigrationRunnerConfig;

  constructor(config: MigrationRunnerConfig) {
    this.config = config;
    this.migrationService = new MigrationService(config.mongoService);
  }

  /**
   * Initialize the migration runner
   */
  async initialize(): Promise<void> {
    try {
      await this.migrationService.initialize();
      Logger.info('Migration runner initialized');
    } catch (error) {
      Logger.error('Failed to initialize migration runner:', error);
      throw new BusinessException('Migration runner initialization failed', { error });
    }
  }

  /**
   * Register migrations from definitions
   */
  registerMigrations(migrations: MigrationDefinition[]): void {
    this.migrationService.registerMigrations(migrations);
  }

  /**
   * Run migrations with options
   */
  async run(options: {
    dryRun?: boolean;
    targetVersion?: string;
    force?: boolean;
  } = {}): Promise<MigrationResult[]> {
    const { dryRun = this.config.dryRun || false, targetVersion, force = false } = options;

    try {
      if (dryRun) {
        Logger.info('Running migrations in dry-run mode');
        return await this.dryRunMigrations(targetVersion);
      }

      Logger.info('Running migrations');
      const results = await this.migrationService.runMigrations();

      if (results.some(r => r.status === 'failed') && !force) {
        throw new BusinessException('Some migrations failed. Use force=true to continue.');
      }

      return results;
    } catch (error) {
      Logger.error('Migration run failed:', error);
      throw error;
    }
  }

  /**
   * Rollback a specific migration
   */
  async rollback(migrationId: string): Promise<MigrationResult> {
    try {
      Logger.info(`Rolling back migration: ${migrationId}`);
      return await this.migrationService.rollbackMigration(migrationId);
    } catch (error) {
      Logger.error(`Rollback failed for migration ${migrationId}:`, error);
      throw error;
    }
  }

  /**
   * Get migration status
   */
  async getStatus(): Promise<any> {
    return await this.migrationService.getMigrationStatus();
  }

  /**
   * Dry run migrations (show what would be executed)
   */
  private async dryRunMigrations(targetVersion?: string): Promise<MigrationResult[]> {
    const status = await this.migrationService.getMigrationStatus();
    const pendingMigrations = status.migrations.filter(m => m.status === 'pending');

    let migrationsToRun = pendingMigrations;
    if (targetVersion) {
      migrationsToRun = pendingMigrations.filter(m => m.id <= targetVersion);
    }

    Logger.info(`Dry run: Would execute ${migrationsToRun.length} migrations:`);
    migrationsToRun.forEach(migration => {
      Logger.info(`  - ${migration.id}: ${migration.name}`);
    });

    return migrationsToRun.map(migration => ({
      migrationId: migration.id,
      status: 'completed' as any,
      executionTimeMs: 0,
    }));
  }

  /**
   * Validate all registered migrations
   */
  async validate(): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      const status = await this.migrationService.getMigrationStatus();
      
      // Check for duplicate migration IDs
      const ids = status.migrations.map(m => m.id);
      const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
      if (duplicates.length > 0) {
        errors.push(`Duplicate migration IDs found: ${duplicates.join(', ')}`);
      }

      // Check for missing dependencies
      // This would require access to the migration definitions
      // Implementation depends on how migrations are loaded

      Logger.info(`Migration validation: ${errors.length === 0 ? 'PASSED' : 'FAILED'}`);
      if (errors.length > 0) {
        errors.forEach(error => Logger.error(`Validation error: ${error}`));
      }

    } catch (error) {
      errors.push(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Reset migrations (dangerous - removes all migration records)
   */
  async reset(confirm: boolean = false): Promise<void> {
    if (!confirm) {
      throw new BusinessException('Reset requires explicit confirmation');
    }

    try {
      const db = await this.config.mongoService.getDatabase();
      await db.collection('migrations').deleteMany({});
      Logger.warn('All migration records have been reset');
    } catch (error) {
      Logger.error('Failed to reset migrations:', error);
      throw new BusinessException('Migration reset failed', { error });
    }
  }

  /**
   * Get the migration service instance
   */
  getMigrationService(): MigrationService {
    return this.migrationService;
  }
}
