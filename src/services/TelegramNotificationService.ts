import TelegramBot from 'node-telegram-bot-api';
import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';
import { TelegramParseMode } from '../types/enums';

export interface NotificationMessage {
  chatId: string | number;
  message: string;
  options?: TelegramBot.SendMessageOptions;
}

export interface NotificationConfig {
  token: string;
  defaultChatId?: string | number;
  parseMode?: TelegramParseMode;
  disableWebPagePreview?: boolean;
}

export class TelegramNotificationService {
  private bot!: TelegramBot;
  private config: NotificationConfig;
  private isInitialized = false;

  constructor(config: NotificationConfig) {
    this.config = config;
    this.initializeBot();
  }

  private initializeBot(): void {
    try {
      this.bot = new TelegramBot(this.config.token, { polling: false });
      this.isInitialized = true;
      Logger.info('Telegram notification service initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Telegram bot:', error);
      throw new BusinessException('Telegram bot initialization failed', { error });
    }
  }

  /**
   * Send a simple text message
   */
  async sendMessage(chatId: string | number, message: string, options?: TelegramBot.SendMessageOptions): Promise<void> {
    if (!this.isInitialized) {
      throw new BusinessException('Telegram bot not initialized');
    }

    try {
      const messageOptions: TelegramBot.SendMessageOptions = {
        parse_mode: this.config.parseMode || 'HTML',
        disable_web_page_preview: this.config.disableWebPagePreview || true,
        ...options,
      };

      await this.bot.sendMessage(chatId, message, messageOptions);
      Logger.info(`Message sent to chat ${chatId}`);
    } catch (error) {
      Logger.error(`Failed to send message to chat ${chatId}:`, error);
      throw new BusinessException('Failed to send Telegram message', { chatId, error });
    }
  }

  /**
   * Send message to default chat if configured
   */
  async sendToDefault(message: string, options?: TelegramBot.SendMessageOptions): Promise<void> {
    if (!this.config.defaultChatId) {
      throw new BusinessException('Default chat ID not configured');
    }

    await this.sendMessage(this.config.defaultChatId, message, options);
  }

  /**
   * Send error notification with formatted message
   */
  async sendErrorNotification(title: string, error: any, chatId?: string | number): Promise<void> {
    const targetChatId = chatId || this.config.defaultChatId;

    if (!targetChatId) {
      throw new BusinessException('No chat ID provided for error notification');
    }

    const errorMessage = this.formatErrorMessage(title, error);
    await this.sendMessage(targetChatId, errorMessage);
  }

  /**
   * Send success notification
   */
  async sendSuccessNotification(title: string, details?: string, chatId?: string | number): Promise<void> {
    const targetChatId = chatId || this.config.defaultChatId;

    if (!targetChatId) {
      throw new BusinessException('No chat ID provided for success notification');
    }

    const message = this.formatSuccessMessage(title, details);
    await this.sendMessage(targetChatId, message);
  }

  /**
   * Send warning notification
   */
  async sendWarningNotification(title: string, details?: string, chatId?: string | number): Promise<void> {
    const targetChatId = chatId || this.config.defaultChatId;

    if (!targetChatId) {
      throw new BusinessException('No chat ID provided for warning notification');
    }

    const message = this.formatWarningMessage(title, details);
    await this.sendMessage(targetChatId, message);
  }

  /**
   * Send bulk messages to multiple chats
   */
  async sendBulkMessages(messages: NotificationMessage[]): Promise<void> {
    const promises = messages.map(({ chatId, message, options }) =>
      this.sendMessage(chatId, message, options).catch((error) => {
        Logger.error(`Failed to send bulk message to ${chatId}:`, error);
        return { chatId, error };
      }),
    );

    const results = await Promise.allSettled(promises);
    const failures = results.filter((result) => result.status === 'rejected');

    if (failures.length > 0) {
      Logger.warn(`${failures.length} bulk messages failed to send`);
    }
  }

  private formatErrorMessage(title: string, error: any): string {
    const timestamp = new Date().toISOString();
    const errorMsg = error?.message || error?.toString() || 'Unknown error';

    return (
      `🚨 <b>ERROR: ${title}</b>\n\n` +
      `⏰ <b>Time:</b> ${timestamp}\n` +
      `❌ <b>Error:</b> <code>${errorMsg}</code>\n\n` +
      `Please check the logs for more details.`
    );
  }

  private formatSuccessMessage(title: string, details?: string): string {
    const timestamp = new Date().toISOString();

    let message = `✅ <b>SUCCESS: ${title}</b>\n\n` + `⏰ <b>Time:</b> ${timestamp}`;

    if (details) {
      message += `\n📝 <b>Details:</b> ${details}`;
    }

    return message;
  }

  private formatWarningMessage(title: string, details?: string): string {
    const timestamp = new Date().toISOString();

    let message = `⚠️ <b>WARNING: ${title}</b>\n\n` + `⏰ <b>Time:</b> ${timestamp}`;

    if (details) {
      message += `\n📝 <b>Details:</b> ${details}`;
    }

    return message;
  }

  /**
   * Get bot information
   */
  async getBotInfo(): Promise<TelegramBot.User> {
    if (!this.isInitialized) {
      throw new BusinessException('Telegram bot not initialized');
    }

    try {
      return await this.bot.getMe();
    } catch (error) {
      Logger.error('Failed to get bot info:', error);
      throw new BusinessException('Failed to get bot information', { error });
    }
  }

  /**
   * Check if bot is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.getBotInfo();
      return true;
    } catch {
      return false;
    }
  }
}
