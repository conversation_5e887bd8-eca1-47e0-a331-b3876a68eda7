import { Db, Document } from 'mongodb';
import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';
import { MongoDBService } from './MongoDBService';

export interface MigrationDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  up: (db: Db) => Promise<void>;
  down?: (db: Db) => Promise<void>;
  dependencies?: string[];
}

export interface MigrationRecord extends Document {
  _id?: string;
  migrationId: string;
  name: string;
  description: string;
  version: string;
  executedAt: Date;
  executionTimeMs: number;
  checksum?: string;
  rollbackAvailable: boolean;
}

export enum MigrationStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ROLLED_BACK = 'rolled_back',
}

export interface MigrationResult {
  migrationId: string;
  status: MigrationStatus;
  executionTimeMs: number;
  error?: string;
}

export class MigrationService {
  private mongoService: MongoDBService;
  private migrationsCollectionName = 'migrations';
  private migrations: Map<string, MigrationDefinition> = new Map();

  constructor(mongoService: MongoDBService) {
    this.mongoService = mongoService;
  }

  /**
   * Register a migration
   */
  registerMigration(migration: MigrationDefinition): void {
    if (this.migrations.has(migration.id)) {
      throw new BusinessException(`Migration with ID ${migration.id} already registered`);
    }

    // Validate migration
    this.validateMigration(migration);
    
    this.migrations.set(migration.id, migration);
    Logger.info(`Registered migration: ${migration.id} - ${migration.name}`);
  }

  /**
   * Register multiple migrations
   */
  registerMigrations(migrations: MigrationDefinition[]): void {
    migrations.forEach(migration => this.registerMigration(migration));
  }

  /**
   * Initialize the migration system
   */
  async initialize(): Promise<void> {
    try {
      await this.ensureMigrationsCollection();
      Logger.info('Migration service initialized');
    } catch (error) {
      Logger.error('Failed to initialize migration service:', error);
      throw new BusinessException('Migration service initialization failed', { error });
    }
  }

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<MigrationResult[]> {
    const results: MigrationResult[] = [];
    
    try {
      const pendingMigrations = await this.getPendingMigrations();
      
      if (pendingMigrations.length === 0) {
        Logger.info('No pending migrations to run');
        return results;
      }

      Logger.info(`Found ${pendingMigrations.length} pending migrations`);

      // Sort migrations by dependencies and version
      const sortedMigrations = this.sortMigrationsByDependencies(pendingMigrations);

      for (const migration of sortedMigrations) {
        const result = await this.executeMigration(migration);
        results.push(result);

        if (result.status === MigrationStatus.FAILED) {
          Logger.error(`Migration ${migration.id} failed, stopping execution`);
          break;
        }
      }

      const successful = results.filter(r => r.status === MigrationStatus.COMPLETED).length;
      const failed = results.filter(r => r.status === MigrationStatus.FAILED).length;
      
      Logger.info(`Migration execution completed: ${successful} successful, ${failed} failed`);

    } catch (error) {
      Logger.error('Error running migrations:', error);
      throw new BusinessException('Migration execution failed', { error });
    }

    return results;
  }

  /**
   * Execute a single migration
   */
  private async executeMigration(migration: MigrationDefinition): Promise<MigrationResult> {
    const startTime = Date.now();
    
    try {
      Logger.info(`Executing migration: ${migration.id} - ${migration.name}`);

      // Mark migration as running
      await this.updateMigrationStatus(migration.id, MigrationStatus.RUNNING);

      const db = await this.mongoService.getDatabase();
      
      // Execute the migration
      await migration.up(db);
      
      const executionTime = Date.now() - startTime;

      // Record successful migration
      await this.recordMigration(migration, executionTime);
      
      Logger.info(`Migration ${migration.id} completed in ${executionTime}ms`);

      return {
        migrationId: migration.id,
        status: MigrationStatus.COMPLETED,
        executionTimeMs: executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      Logger.error(`Migration ${migration.id} failed:`, error);
      
      // Mark migration as failed
      await this.updateMigrationStatus(migration.id, MigrationStatus.FAILED);

      return {
        migrationId: migration.id,
        status: MigrationStatus.FAILED,
        executionTimeMs: executionTime,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Rollback a migration
   */
  async rollbackMigration(migrationId: string): Promise<MigrationResult> {
    const migration = this.migrations.get(migrationId);
    if (!migration) {
      throw new BusinessException(`Migration ${migrationId} not found`);
    }

    if (!migration.down) {
      throw new BusinessException(`Migration ${migrationId} does not support rollback`);
    }

    const startTime = Date.now();

    try {
      Logger.info(`Rolling back migration: ${migrationId}`);

      const db = await this.mongoService.getDatabase();
      await migration.down(db);

      const executionTime = Date.now() - startTime;

      // Remove migration record
      await this.removeMigrationRecord(migrationId);

      Logger.info(`Migration ${migrationId} rolled back in ${executionTime}ms`);

      return {
        migrationId,
        status: MigrationStatus.ROLLED_BACK,
        executionTimeMs: executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      Logger.error(`Rollback of migration ${migrationId} failed:`, error);

      return {
        migrationId,
        status: MigrationStatus.FAILED,
        executionTimeMs: executionTime,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{
    total: number;
    executed: number;
    pending: number;
    migrations: Array<{
      id: string;
      name: string;
      status: MigrationStatus;
      executedAt?: Date;
    }>;
  }> {
    const executedMigrations = await this.getExecutedMigrations();
    const executedIds = new Set(executedMigrations.map(m => m.migrationId));

    const migrations = Array.from(this.migrations.values()).map(migration => ({
      id: migration.id,
      name: migration.name,
      status: executedIds.has(migration.id) ? MigrationStatus.COMPLETED : MigrationStatus.PENDING,
      executedAt: executedMigrations.find(m => m.migrationId === migration.id)?.executedAt,
    }));

    return {
      total: this.migrations.size,
      executed: executedMigrations.length,
      pending: this.migrations.size - executedMigrations.length,
      migrations,
    };
  }

  /**
   * Validate migration definition
   */
  private validateMigration(migration: MigrationDefinition): void {
    if (!migration.id || !migration.name || !migration.version) {
      throw new BusinessException('Migration must have id, name, and version');
    }

    if (!migration.up || typeof migration.up !== 'function') {
      throw new BusinessException('Migration must have an up function');
    }

    if (migration.down && typeof migration.down !== 'function') {
      throw new BusinessException('Migration down must be a function if provided');
    }

    // Validate version format (semantic versioning)
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(migration.version)) {
      throw new BusinessException('Migration version must follow semantic versioning (x.y.z)');
    }
  }

  /**
   * Ensure migrations collection exists
   */
  private async ensureMigrationsCollection(): Promise<void> {
    const db = await this.mongoService.getDatabase();
    const collections = await db.listCollections({ name: this.migrationsCollectionName }).toArray();
    
    if (collections.length === 0) {
      await db.createCollection(this.migrationsCollectionName);
      
      // Create indexes
      const migrationsCollection = db.collection(this.migrationsCollectionName);
      await migrationsCollection.createIndex({ migrationId: 1 }, { unique: true });
      await migrationsCollection.createIndex({ executedAt: 1 });
      
      Logger.info('Created migrations collection with indexes');
    }
  }

  /**
   * Get pending migrations
   */
  private async getPendingMigrations(): Promise<MigrationDefinition[]> {
    const executedMigrations = await this.getExecutedMigrations();
    const executedIds = new Set(executedMigrations.map(m => m.migrationId));

    return Array.from(this.migrations.values()).filter(
      migration => !executedIds.has(migration.id)
    );
  }

  /**
   * Get executed migrations
   */
  private async getExecutedMigrations(): Promise<MigrationRecord[]> {
    const db = await this.mongoService.getDatabase();
    const migrationsCollection = db.collection<MigrationRecord>(this.migrationsCollectionName);
    
    return await migrationsCollection
      .find({})
      .sort({ executedAt: 1 })
      .toArray();
  }

  /**
   * Record successful migration
   */
  private async recordMigration(migration: MigrationDefinition, executionTimeMs: number): Promise<void> {
    const db = await this.mongoService.getDatabase();
    const migrationsCollection = db.collection<MigrationRecord>(this.migrationsCollectionName);

    const record: MigrationRecord = {
      migrationId: migration.id,
      name: migration.name,
      description: migration.description,
      version: migration.version,
      executedAt: new Date(),
      executionTimeMs,
      rollbackAvailable: !!migration.down,
    };

    await migrationsCollection.insertOne(record);
  }

  /**
   * Update migration status (for tracking running/failed states)
   */
  private async updateMigrationStatus(migrationId: string, status: MigrationStatus): Promise<void> {
    // This could be implemented with a separate status tracking if needed
    // For now, we only record completed migrations
    if (status === MigrationStatus.RUNNING || status === MigrationStatus.FAILED) {
      Logger.debug(`Migration ${migrationId} status: ${status}`);
    }
  }

  /**
   * Remove migration record (for rollbacks)
   */
  private async removeMigrationRecord(migrationId: string): Promise<void> {
    const db = await this.mongoService.getDatabase();
    const migrationsCollection = db.collection<MigrationRecord>(this.migrationsCollectionName);
    
    await migrationsCollection.deleteOne({ migrationId });
  }

  /**
   * Sort migrations by dependencies and version
   */
  private sortMigrationsByDependencies(migrations: MigrationDefinition[]): MigrationDefinition[] {
    // Simple topological sort based on dependencies and version
    const sorted: MigrationDefinition[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (migration: MigrationDefinition) => {
      if (visiting.has(migration.id)) {
        throw new BusinessException(`Circular dependency detected in migration ${migration.id}`);
      }
      
      if (visited.has(migration.id)) {
        return;
      }

      visiting.add(migration.id);

      // Process dependencies first
      if (migration.dependencies) {
        for (const depId of migration.dependencies) {
          const dependency = migrations.find(m => m.id === depId);
          if (dependency) {
            visit(dependency);
          }
        }
      }

      visiting.delete(migration.id);
      visited.add(migration.id);
      sorted.push(migration);
    };

    // Sort by version first, then process dependencies
    const versionSorted = migrations.sort((a, b) => a.version.localeCompare(b.version));
    
    for (const migration of versionSorted) {
      visit(migration);
    }

    return sorted;
  }
}
