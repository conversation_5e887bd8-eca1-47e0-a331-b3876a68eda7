import { io, Socket } from 'socket.io-client';
import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';
import { EventEmitter } from 'events';
import { SocketTransport } from '../types/enums';

export interface SocketConfig {
  url: string;
  options?: {
    reconnection?: boolean;
    reconnectionAttempts?: number;
    reconnectionDelay?: number;
    timeout?: number;
    auth?: Record<string, any>;
    transports?: SocketTransport[];
  };
}

export interface SocketMessage {
  event: string;
  data: any;
  timestamp?: number;
}

export class SocketHandler extends EventEmitter {
  private socket: Socket | null = null;
  private config: SocketConfig;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private messageQueue: SocketMessage[] = [];
  private connectionPromise: Promise<void> | null = null;

  constructor(config: SocketConfig) {
    super();
    this.config = config;
    this.maxReconnectAttempts = config.options?.reconnectionAttempts || 5;
  }

  /**
   * Connect to the socket server
   */
  async connect(): Promise<void> {
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = this.establishConnection();
    return this.connectionPromise;
  }

  private async establishConnection(): Promise<void> {
    try {
      Logger.info(`Connecting to socket server: ${this.config.url}`);

      const defaultOptions = {
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        timeout: 20000,
        transports: [SocketTransport.WEBSOCKET, SocketTransport.POLLING],
      };

      this.socket = io(this.config.url, {
        ...defaultOptions,
        ...this.config.options,
      });

      this.setupEventHandlers();

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new BusinessException('Socket connection timeout'));
        }, this.config.options?.timeout || 20000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.connectionPromise = null;
          Logger.info('Socket connected successfully');
          this.processMessageQueue();
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          this.connectionPromise = null;
          Logger.error('Socket connection error:', error);
          reject(new BusinessException('Socket connection failed', { error }));
        });
      });
    } catch (error) {
      this.connectionPromise = null;
      Logger.error('Failed to establish socket connection:', error);
      throw new BusinessException('Socket connection establishment failed', { error });
    }
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      Logger.info('Socket connected');
      this.emit('connected');
      this.processMessageQueue();
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      Logger.warn(`Socket disconnected: ${reason}`);
      this.emit('disconnected', reason);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true;
      Logger.info(`Socket reconnected after ${attemptNumber} attempts`);
      this.emit('reconnected', attemptNumber);
      this.processMessageQueue();
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.reconnectAttempts = attemptNumber;
      Logger.info(`Socket reconnection attempt ${attemptNumber}`);
      this.emit('reconnectAttempt', attemptNumber);
    });

    this.socket.on('reconnect_error', (error) => {
      Logger.error('Socket reconnection error:', error);
      this.emit('reconnectError', error);
    });

    this.socket.on('reconnect_failed', () => {
      Logger.error('Socket reconnection failed - max attempts reached');
      this.emit('reconnectFailed');
    });

    // Handle custom events
    this.socket.onAny((eventName, ...args) => {
      Logger.debug(`Socket event received: ${eventName}`, args);
      this.emit('message', { event: eventName, data: args, timestamp: Date.now() });
    });
  }

  /**
   * Send a message through the socket
   */
  async send(event: string, data: any): Promise<void> {
    const message: SocketMessage = {
      event,
      data,
      timestamp: Date.now(),
    };

    if (!this.isConnected) {
      Logger.warn(`Socket not connected, queuing message: ${event}`);
      this.messageQueue.push(message);
      return;
    }

    try {
      this.socket!.emit(event, data);
      Logger.debug(`Socket message sent: ${event}`);
    } catch (error) {
      Logger.error(`Failed to send socket message: ${event}`, error);
      throw new BusinessException('Failed to send socket message', { event, error });
    }
  }

  /**
   * Send a message and wait for acknowledgment
   */
  async sendWithAck(event: string, data: any, timeout = 5000): Promise<any> {
    if (!this.isConnected) {
      throw new BusinessException('Socket not connected');
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new BusinessException('Socket acknowledgment timeout', { event }));
      }, timeout);

      this.socket!.emit(event, data, (response: any) => {
        clearTimeout(timer);
        resolve(response);
      });
    });
  }

  /**
   * Subscribe to a specific event
   */
  subscribe(event: string, callback: (data: any) => void): void {
    if (!this.socket) {
      throw new BusinessException('Socket not initialized');
    }

    this.socket.on(event, callback);
    Logger.debug(`Subscribed to socket event: ${event}`);
  }

  /**
   * Unsubscribe from a specific event
   */
  unsubscribe(event: string, callback?: (data: any) => void): void {
    if (!this.socket) return;

    if (callback) {
      this.socket.off(event, callback);
    } else {
      this.socket.removeAllListeners(event);
    }
    Logger.debug(`Unsubscribed from socket event: ${event}`);
  }

  /**
   * Process queued messages when connection is restored
   */
  private processMessageQueue(): void {
    if (this.messageQueue.length === 0) return;

    Logger.info(`Processing ${this.messageQueue.length} queued messages`);

    const messages = [...this.messageQueue];
    this.messageQueue = [];

    messages.forEach(({ event, data }) => {
      this.send(event, data).catch((error) => {
        Logger.error(`Failed to send queued message: ${event}`, error);
      });
    });
  }

  /**
   * Disconnect from the socket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      Logger.info('Socket disconnected');
    }
  }

  /**
   * Check if socket is connected
   */
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    queuedMessages: number;
  } {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
    };
  }

  /**
   * Health check for the socket connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isSocketConnected()) {
        return false;
      }

      // Send a ping and wait for response
      await this.sendWithAck('ping', { timestamp: Date.now() }, 3000);
      return true;
    } catch {
      return false;
    }
  }
}
