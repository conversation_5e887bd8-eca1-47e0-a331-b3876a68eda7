import Logger from '@libs/Logger';
import { BusinessException } from '@libs/index';
import { TelegramNotificationService, NotificationConfig } from './TelegramNotificationService';
import { SocketHandler, SocketConfig } from './SocketHandler';
import { MongoDBService, MongoDBConfig } from './MongoDBService';
import { MigrationRunner } from './MigrationRunner';
import { migrations } from '../migrations';
import { NotificationType } from '../types/enums';

export interface ServiceManagerConfig {
  telegram?: {
    error?: NotificationConfig;
    general?: NotificationConfig;
  };
  socket?: SocketConfig;
  mongodb?: MongoDBConfig;
  migrations?: {
    autoRun?: boolean;
    dryRun?: boolean;
  };
}

export class ServiceManager {
  private telegramErrorService: TelegramNotificationService | null = null;
  private telegramGeneralService: TelegramNotificationService | null = null;
  private socketHandler: SocketHandler | null = null;
  private mongoDBService: MongoDBService | null = null;
  private migrationRunner: MigrationRunner | null = null;
  private isInitialized = false;

  constructor(private config: ServiceManagerConfig) {}

  /**
   * Initialize all configured services
   */
  async initialize(): Promise<void> {
    try {
      Logger.info('Initializing services...');

      // Initialize Telegram services if configured
      if (this.config.telegram?.error) {
        this.telegramErrorService = new TelegramNotificationService(this.config.telegram.error);
        Logger.info('Telegram error notification service initialized');
      }

      if (this.config.telegram?.general) {
        this.telegramGeneralService = new TelegramNotificationService(this.config.telegram.general);
        Logger.info('Telegram general notification service initialized');
      }

      // Initialize Socket handler if configured
      if (this.config.socket) {
        this.socketHandler = new SocketHandler(this.config.socket);
        await this.socketHandler.connect();
        this.setupSocketEventHandlers();
        Logger.info('Socket handler initialized');
      }

      // Initialize MongoDB service if configured
      if (this.config.mongodb) {
        this.mongoDBService = new MongoDBService(this.config.mongodb);
        await this.mongoDBService.connect();
        Logger.info('MongoDB service initialized');

        // Initialize migration runner if MongoDB is available
        if (this.config.migrations === undefined || this.config.migrations) {
          this.migrationRunner = new MigrationRunner({
            mongoService: this.mongoDBService,
            autoRun: this.config.migrations?.autoRun ?? false,
            dryRun: this.config.migrations?.dryRun ?? false,
          });

          await this.migrationRunner.initialize();
          this.migrationRunner.registerMigrations(migrations);
          Logger.info('Migration runner initialized');

          // Auto-run migrations if configured
          if (this.config.migrations?.autoRun) {
            Logger.info('Auto-running migrations...');
            const results = await this.migrationRunner.run({
              dryRun: this.config.migrations?.dryRun,
            });

            const failed = results.filter((r) => r.status === 'failed');
            if (failed.length > 0) {
              Logger.error(`${failed.length} migrations failed during auto-run`);
              throw new BusinessException('Migration auto-run failed', { failedMigrations: failed });
            }

            Logger.info(`Auto-run completed: ${results.length} migrations executed`);
          }
        }
      }

      this.isInitialized = true;
      Logger.info('All services initialized successfully');

      // Send initialization notification if Telegram general service is available
      if (this.telegramGeneralService) {
        await this.telegramGeneralService
          .sendSuccessNotification('Services Initialized', 'All application services have been started successfully')
          .catch((error: any) => {
            Logger.warn('Failed to send initialization notification:', error);
          });
      }
    } catch (error) {
      Logger.error('Failed to initialize services:', error);
      throw new BusinessException('Service initialization failed', { error });
    }
  }

  /**
   * Setup socket event handlers for integration with other services
   */
  private setupSocketEventHandlers(): void {
    if (!this.socketHandler) return;

    this.socketHandler.on('connected', () => {
      Logger.info('Socket connected - notifying via Telegram');
      this.telegramGeneralService
        ?.sendSuccessNotification('Socket Connected', 'Socket connection established successfully')
        .catch((error: any) => {
          Logger.warn('Failed to send socket connection notification:', error);
        });
    });

    this.socketHandler.on('disconnected', (reason) => {
      Logger.warn(`Socket disconnected: ${reason}`);
      this.telegramGeneralService
        ?.sendWarningNotification('Socket Disconnected', `Connection lost: ${reason}`)
        .catch((error: any) => {
          Logger.warn('Failed to send socket disconnection notification:', error);
        });
    });

    this.socketHandler.on('reconnectFailed', () => {
      Logger.error('Socket reconnection failed');
      this.telegramErrorService
        ?.sendErrorNotification(
          'Socket Reconnection Failed',
          'Failed to reconnect to socket server after maximum attempts',
        )
        .catch((error: any) => {
          Logger.warn('Failed to send socket reconnection failure notification:', error);
        });
    });

    // Handle incoming socket messages
    this.socketHandler.on('message', (message) => {
      this.handleSocketMessage(message);
    });
  }

  /**
   * Handle incoming socket messages
   */
  private handleSocketMessage(message: any): void {
    try {
      Logger.debug('Processing socket message:', message);

      // Example: Handle notification requests from socket
      if (message.event === 'notification') {
        this.handleNotificationRequest(message.data);
      }

      // Example: Handle error reports from socket
      if (message.event === 'error') {
        this.handleErrorReport(message.data);
      }

      // Add more message handlers as needed
    } catch (error) {
      Logger.error('Failed to handle socket message:', error);
    }
  }

  /**
   * Handle notification requests from socket
   */
  private async handleNotificationRequest(data: any): Promise<void> {
    const hasGeneralService = this.telegramGeneralService !== null;
    const hasErrorService = this.telegramErrorService !== null;

    if (!hasGeneralService && !hasErrorService) {
      Logger.warn('Notification requested but no Telegram services available');
      return;
    }

    try {
      const { type, title, message, chatId } = data;

      switch (type) {
        case NotificationType.SUCCESS:
          if (hasGeneralService) {
            await this.telegramGeneralService!.sendSuccessNotification(title, message, chatId);
          }
          break;
        case NotificationType.WARNING:
          if (hasGeneralService) {
            await this.telegramGeneralService!.sendWarningNotification(title, message, chatId);
          }
          break;
        case NotificationType.ERROR:
          if (hasErrorService) {
            await this.telegramErrorService!.sendErrorNotification(title, message, chatId);
          } else if (hasGeneralService) {
            await this.telegramGeneralService!.sendErrorNotification(title, message, chatId);
          }
          break;
        default:
          if (hasGeneralService) {
            const defaultChatId = this.config.telegram?.general?.defaultChatId;
            await this.telegramGeneralService!.sendMessage(chatId || defaultChatId!, message);
          }
      }
    } catch (error) {
      Logger.error('Failed to handle notification request:', error);
    }
  }

  /**
   * Handle error reports from socket
   */
  private async handleErrorReport(data: any): Promise<void> {
    const errorService = this.telegramErrorService || this.telegramGeneralService;
    if (!errorService) return;

    try {
      await errorService.sendErrorNotification(
        data.title || 'Socket Error Report',
        data.error || data.message || 'Unknown error reported via socket',
      );
    } catch (error) {
      Logger.error('Failed to handle error report:', error);
    }
  }

  /**
   * Send notification through appropriate Telegram service
   */
  async sendNotification(
    type: NotificationType,
    title: string,
    details?: string,
    chatId?: string | number,
  ): Promise<void> {
    const hasGeneralService = this.telegramGeneralService !== null;
    const hasErrorService = this.telegramErrorService !== null;

    if (!hasGeneralService && !hasErrorService) {
      throw new BusinessException('No Telegram services available');
    }

    switch (type) {
      case NotificationType.SUCCESS:
        if (hasGeneralService) {
          await this.telegramGeneralService!.sendSuccessNotification(title, details, chatId);
        } else {
          throw new BusinessException('General Telegram service not available for success notifications');
        }
        break;
      case NotificationType.WARNING:
        if (hasGeneralService) {
          await this.telegramGeneralService!.sendWarningNotification(title, details, chatId);
        } else {
          throw new BusinessException('General Telegram service not available for warning notifications');
        }
        break;
      case NotificationType.ERROR:
        if (hasErrorService) {
          await this.telegramErrorService!.sendErrorNotification(title, details, chatId);
        } else if (hasGeneralService) {
          await this.telegramGeneralService!.sendErrorNotification(title, details, chatId);
        } else {
          throw new BusinessException('No Telegram service available for error notifications');
        }
        break;
      case NotificationType.MESSAGE:
        if (hasGeneralService) {
          const defaultChatId = this.config.telegram?.general?.defaultChatId;
          await this.telegramGeneralService!.sendMessage(
            chatId || defaultChatId!,
            `${title}${details ? `\n\n${details}` : ''}`,
          );
        } else {
          throw new BusinessException('General Telegram service not available for messages');
        }
        break;
    }
  }

  /**
   * Send message through socket
   */
  async sendSocketMessage(event: string, data: any): Promise<void> {
    if (!this.socketHandler) {
      throw new BusinessException('Socket handler not available');
    }

    await this.socketHandler.send(event, data);
  }

  /**
   * Get service status
   */
  getStatus(): {
    initialized: boolean;
    telegram: {
      error: boolean;
      general: boolean;
    };
    socket: boolean;
    mongodb: boolean;
    socketStatus?: any;
    mongodbStatus?: any;
  } {
    return {
      initialized: this.isInitialized,
      telegram: {
        error: this.telegramErrorService !== null,
        general: this.telegramGeneralService !== null,
      },
      socket: this.socketHandler !== null,
      mongodb: this.mongoDBService !== null,
      socketStatus: this.socketHandler?.getStatus(),
      mongodbStatus: this.mongoDBService?.getConnectionInfo(),
    };
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{
    overall: boolean;
    telegram: boolean;
    socket: boolean;
    mongodb: boolean;
  }> {
    const errorServiceHealth = this.telegramErrorService ? await this.telegramErrorService.healthCheck() : true;
    const generalServiceHealth = this.telegramGeneralService ? await this.telegramGeneralService.healthCheck() : true;
    const telegramHealth = errorServiceHealth && generalServiceHealth;
    const socketHealth = this.socketHandler ? await this.socketHandler.healthCheck() : true;
    const mongodbHealth = this.mongoDBService ? await this.mongoDBService.healthCheck() : true;

    return {
      overall: telegramHealth && socketHealth && mongodbHealth,
      telegram: telegramHealth,
      socket: socketHealth,
      mongodb: mongodbHealth,
    };
  }

  /**
   * Shutdown all services
   */
  async shutdown(): Promise<void> {
    Logger.info('Shutting down services...');

    // Send shutdown notification via general service if available
    if (this.telegramGeneralService) {
      await this.telegramGeneralService
        .sendWarningNotification('Service Shutdown', 'Application services are shutting down')
        .catch((error: any) => {
          Logger.warn('Failed to send shutdown notification:', error);
        });
    }

    if (this.socketHandler) {
      this.socketHandler.disconnect();
    }

    if (this.mongoDBService) {
      await this.mongoDBService.disconnect();
    }

    this.isInitialized = false;
    Logger.info('All services shut down');
  }

  // Getters for direct service access
  get telegramError(): TelegramNotificationService | null {
    return this.telegramErrorService;
  }

  get telegramGeneral(): TelegramNotificationService | null {
    return this.telegramGeneralService;
  }

  get socket(): SocketHandler | null {
    return this.socketHandler;
  }

  get mongodb(): MongoDBService | null {
    return this.mongoDBService;
  }

  get migrations(): MigrationRunner | null {
    return this.migrationRunner;
  }
}
