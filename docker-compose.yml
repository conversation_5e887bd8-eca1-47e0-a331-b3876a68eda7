version: '3.8'

services:
  db:
    image: mysql:latest
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      MYSQL_DATABASE: "nami-bot"
      MYSQL_ROOT_PASSWORD: 1
    ports:
      - "3308:3306"
  redis:
    image: redis
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "6380:6379"
  mongodb:
    image: mongo:latest
    command: --auth
    ports:
      - 27017:27017
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=james
      - <PERSON><PERSON><PERSON><PERSON>_INITDB_DATABASE=zizi
