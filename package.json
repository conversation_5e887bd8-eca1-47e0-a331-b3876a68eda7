{"name": "titan-bot-process", "version": "1.0.0", "description": "Modern TypeScript bot process application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/index.js", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "dev:watch": "ts-node -r tsconfig-paths/register --watch src/index.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "ts-node -r tsconfig-paths/register src/cli/migrate.ts", "migrate:up": "npm run migrate up", "migrate:down": "npm run migrate down", "migrate:status": "npm run migrate status", "migrate:validate": "npm run migrate validate"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.30.1", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/mongodb": "^4.0.6", "@types/node": "^24.0.10", "@types/node-telegram-bot-api": "^0.63.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "prettier": "^3.6.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.10.0", "bignumber.js": "^9.1.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "lodash": "^4.17.21", "mongodb": "^6.17.0", "mysql": "^2.18.1", "mysql2": "^3.14.1", "node-telegram-bot-api": "^0.64.0", "query-string": "^8.1.0", "redis": "^5.5.6", "socket.io-client": "^4.7.2", "tscpaths": "^0.0.9", "tslib": "^2.8.1", "winston": "^3.17.0"}}